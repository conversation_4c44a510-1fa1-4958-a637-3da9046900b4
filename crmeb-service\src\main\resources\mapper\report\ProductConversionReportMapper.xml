<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zbkj.service.dao.ProductConversionReportDao">

    <select id="getProductConversionReport" resultType="com.zbkj.common.response.ProductConversionReportResponse" parameterType="Map">
        SELECT
        COALESCE(pn.product_code, ss.product_code, os.product_code) AS productCode,
        pn.product_name AS productName,
        COALESCE(ss.sms_total, 0) AS smsTotal,
        COALESCE(ss.sms_success, 0) AS smsSuccess,
        CONCAT(ROUND(IFNULL(ss.sms_success / NULLIF(ss.sms_total, 0), 0) * 100, 2), '%') AS smsSuccessRate,
        COALESCE(os.order_total, 0) AS orderTotal,
        COALESCE(os.order_success, 0) AS orderSuccess,
        COALESCE(so.order_success_final,0) AS orderSuccessCount,
        CONCAT(ROUND(IFNULL(os.order_success / NULLIF(os.order_total, 0), 0) * 100, 2), '%') AS orderSuccessRate,
        CONCAT(ROUND(IFNULL(so.order_success_final / NULLIF(ss.sms_total, 0), 0) * 100, 2), '%') AS productConversionRate
        FROM
        (
        SELECT
        JSON_UNQUOTE(JSON_EXTRACT(params, '$.productCode')) AS product_code,
        COUNT(*) AS sms_total,
        SUM(CASE WHEN result LIKE '%200%' THEN 1 ELSE 0 END) AS sms_success
        FROM eb_open_log
        WHERE handle_type = 1
        AND JSON_VALID(params)
        <if test="startTime != null and startTime != ''">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY product_code
        ) ss
        LEFT JOIN
        (
        SELECT
        JSON_UNQUOTE(JSON_EXTRACT(params, '$.productCode')) AS product_code,
        COUNT(*) AS order_total,
        SUM(CASE WHEN result LIKE '%200%' THEN 1 ELSE 0 END) AS order_success
        FROM eb_open_log
        WHERE handle_type = 2
        AND JSON_VALID(params)
        <if test="startTime != null and startTime != ''">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY product_code
        ) os ON ss.product_code = os.product_code
        LEFT JOIN
        (
        SELECT
        dl.sku AS product_code,
        COUNT(*) AS order_success_final
        FROM eb_order o
        LEFT JOIN eb_order_detail dl ON dl.order_no = o.order_no
        WHERE o.`status` = 6
        AND o.is_del = 0
        <if test="startTime != null and startTime != ''">
            AND o.create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND o.create_time &lt;= #{endTime}
        </if>
        GROUP BY dl.sku
        ) so ON ss.product_code = so.product_code
        LEFT JOIN
        (
        SELECT DISTINCT
        b.bar_code AS product_code,
        a.name AS product_name
        FROM eb_product a
        JOIN eb_product_attr_value b ON a.id = b.product_id
        ) pn ON ss.product_code = pn.product_code
        ORDER BY ss.product_code
    </select>

</mapper>
