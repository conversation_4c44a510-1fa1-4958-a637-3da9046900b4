package com.zbkj.front.service.impl.exterior;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zbkj.common.constants.OrderConstants;
import com.zbkj.common.enums.ExteriorTypeEnum;
import com.zbkj.common.enums.OpenHandleTypeEnum;
import com.zbkj.common.model.order.Order;
import com.zbkj.common.result.CommonResult;
import com.zbkj.common.vo.ExteriorOrderCallbackResponse;
import com.zbkj.common.vo.ExteriorSendSmsRequest;
import com.zbkj.front.exterior.base.AbstractExteriorBase;
import com.zbkj.front.exterior.vo.*;
import com.zbkj.front.exterior.vo.xuecheng.XueChengCallbackRequest;
import com.zbkj.service.service.OpenLogService;
import com.zbkj.service.service.OrderService;
import com.zbkj.service.service.impl.MerchantOpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 学诚接口实现类
 *
 * <AUTHOR>
 * @date 2025/1/15 10:00
 */
@Component("xueChengService")
@Slf4j
public class XueChengService implements AbstractExteriorBase {

    private final String baseUrl;
    private final String cpid;
    private final String appkey;

    @Resource
    private MerchantOpenService merchantOpenService;

    @Autowired
    private OpenLogService openLogService;

    @Autowired
    private OrderService orderService;

    public XueChengService(
            @Value("${exterior.xuecheng.base-url}") String baseUrl,
            @Value("${exterior.xuecheng.cpid}") String cpid,
            @Value("${exterior.xuecheng.appkey}") String appkey
    ) {
        this.baseUrl = baseUrl;
        this.cpid = cpid;
        this.appkey = appkey;
    }

    @Override
    public CommonResult<ExteriorSendSmsResponse> sendSms(ExteriorSendSmsRequest request) throws Exception {
        Map<String, Object> params = new TreeMap<>();
        params.put("channel", request.getProductCode());
        params.put("cpid", Integer.parseInt(cpid));
        params.put("mobile", request.getPhone());
        params.put("ip", request.getIp());
        params.put("corp_type", getCorpType(request.getPhone())); // 根据手机号判断运营商
        params.put("ver", "1.0");
        params.put("time", String.valueOf(DateUtil.currentSeconds()));
        params.put("ua", request.getUa());
        params.put("package", request.getAppPackage());
        params.put("platform", request.getFirstContact());

        // 生成签名
        String sign = generateSign(params);
        params.put("sign", sign);

        // 发送请求
        String response = HttpRequest.get(baseUrl + "/Home/OpenApi/order")
                .form(params)
                .execute()
                .body();
        log.info("学诚短信发送请求：{},结果：{}", JSONUtil.toJsonStr(params), response);
        return parseSmsResponse(response);
    }

    @Override
    public CommonResult<ExteriorCreateOrderResponse> createOrder(ExteriorOrderRequest request) throws Exception {
        Map<String, Object> params = new TreeMap<>();
        params.put("orderid", request.getSendSmsRequest().getChannelOrderNo());
        params.put("verify_code", Integer.parseInt(request.getSmsCode()));
        params.put("mobile", request.getPhone());

        // 生成签名
        String sign = generateSign(params);
        params.put("sign", sign);

        // 发送请求
        String response = HttpRequest.get(baseUrl + "/Home/OpenApi/commit")
                .form(params)
                .execute()
                .body();
        log.info("学诚创建订单发送请求：{},结果：{}", JSONUtil.toJsonStr(params), response);
        return parseOrderResponse(response, request.getSendSmsRequest().getChannelOrderNo());
    }

    @Override
    public CommonResult<ExteriorQueryOrderResponse> queryOrder(ExteriorQueryOrderRequest request) throws Exception {
        // 学诚接口文档中未提供查询接口，此处仅作示例
        return CommonResult.failed("查询功能暂未实现");
    }

    @Override
    public <T> CommonResult handleCallback(T request) {
        XueChengCallbackRequest xcRequest = null;
        String jsonString = null;
        try {
            if (!(request instanceof XueChengCallbackRequest)) {
                return CommonResult.failed("回调请求类型错误");
            }

            xcRequest = (XueChengCallbackRequest) request;

            // 转换为通用回调请求
            ExteriorOrderCallbackResponse callbackResponse = convertToExteriorCallback(xcRequest);
            Order order = orderService.getByChannelOrderNo(xcRequest.getOrderid());
            if (order != null) {
                callbackResponse.setPlatOrderNo(order.getOrderNo());
            }
            // 回调下游
            CommonResult commonResult = merchantOpenService.handleCallback(ExteriorTypeEnum.XUECHENG, callbackResponse);
            Map<String, Object> result = new HashMap<>();
            if (CommonResult.success().getCode() == commonResult.getCode()) {
                return CommonResult.success("ok");
            } else {
                return CommonResult.failed("fail");
            }
        } catch (Exception e) {
            log.error("学诚回调处理异常", e);
            return CommonResult.failed("fail");
        } finally {
            openLogService.saveLog(
                    xcRequest != null ? xcRequest.getOrderid() : null,
                    OpenHandleTypeEnum.CALLBACK,
                    ExteriorTypeEnum.XUECHENG,
                    "",
                    JSONUtil.toJsonStr(request),
                    jsonString
            );
        }
    }

    /**
     * 生成签名
     */
    private String generateSign(Map<String, Object> params) {
        StringBuilder signBuilder = new StringBuilder();

        // 按key升序排列并拼接参数
        params.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !entry.getValue().toString().isEmpty())
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    if (signBuilder.length() > 0) {
                        signBuilder.append("&");
                    }
                    signBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                });

        // 添加appkey
        signBuilder.append(":").append(appkey);

        // MD5签名
        return DigestUtil.md5Hex(signBuilder.toString()).toLowerCase();
    }

    /**
     * 根据手机号判断运营商
     */
    private String getCorpType(String mobile) {
        if (StrUtil.isBlank(mobile) || mobile.length() != 11) {
            return "1"; // 默认移动
        }

        String prefix = mobile.substring(0, 3);
        // 移动：134-139, 147, 150-152, 157-159, 178, 182-184, 187-188, 198
        if (prefix.matches("13[4-9]|147|15[0-2]|15[7-9]|178|18[2-4]|18[7-8]|198")) {
            return "1";
        }
        // 联通：130-132, 145, 155-156, 166, 175-176, 185-186
        else if (prefix.matches("13[0-2]|145|15[5-6]|166|17[5-6]|18[5-6]")) {
            return "2";
        }
        // 电信：133, 149, 153, 173, 177, 180-181, 189, 199
        else if (prefix.matches("133|149|153|173|177|18[0-1]|189|199")) {
            return "3";
        }

        return "1"; // 默认移动
    }

    /**
     * 转换订单状态
     */
    private Integer convertOrderStatus(Integer status) {
        if (status == null) {
            return OrderConstants.ORDER_STATUS_CANCEL;
        }

        switch (status) {
            case 1: // 订购
                return OrderConstants.ORDER_STATUS_COMPLETE;
            case 3: // 退订
                return OrderConstants.ORDER_STATUS_VIRTUAL_UNSUBSCRIBE;
            default: // 其他失败
                return OrderConstants.ORDER_STATUS_VIRTUAL_FAILED;
        }
    }

    /**
     * 转换为通用回调对象
     */
    private ExteriorOrderCallbackResponse convertToExteriorCallback(XueChengCallbackRequest request) {
        ExteriorOrderCallbackResponse response = new ExteriorOrderCallbackResponse();
        response.setStatus(convertOrderStatus(request.getStatus()));
        //response.setPlatOrderNo(request.getExtra()); // 透传参数作为平台订单号
        response.setChannelOrderNo(request.getOrderid());
        response.setPhone(request.getMobile());
        response.setExteriorTypeEnum(ExteriorTypeEnum.XUECHENG);
        Map<String, String> attachParams = new HashMap<>();
        attachParams.put("extra", request.getExtra());
        response.setAttachParams(attachParams);

        return response;
    }

    /**
     * 解析短信响应
     */
    private CommonResult<ExteriorSendSmsResponse> parseSmsResponse(String json) {
        JSONObject jsonObj = JSONUtil.parseObj(json);
        Integer code = jsonObj.getInt("code");

        if (code != null && (code == 0 || code == 200)) {
            String orderid = jsonObj.getStr("orderid");
            if (StrUtil.isBlank(orderid)) {
                log.error("学诚短信接口失败: {}", json);
                return CommonResult.failed(jsonObj.getStr("msg"));
            }
            ExteriorSendSmsResponse response = new ExteriorSendSmsResponse();
            response.setChannelOrderNo(orderid);
            return CommonResult.success(response);
        } else {
            log.error("学诚短信接口失败: {}", json);
            return CommonResult.failed(jsonObj.getStr("msg"));
        }
    }

    /**
     * 解析订单响应
     */
    private CommonResult<ExteriorCreateOrderResponse> parseOrderResponse(String json, String channelOrderNo) {
        JSONObject jsonObj = JSONUtil.parseObj(json);
        Integer code = jsonObj.getInt("code");

        if (code != null && code == 0) {
            ExteriorCreateOrderResponse response = new ExteriorCreateOrderResponse();
            response.setChannelOrderNo(channelOrderNo);
            return CommonResult.success(response);
        } else {
            log.error("学诚创建订单失败: {}", json);
            return CommonResult.failed(jsonObj.getStr("msg"));
        }
    }
}
