# 悦智投存量业务统一接口文档

## 基本信息

- 生产地址:  
  baseUrl = "http://yzt.yfyue.cn:8877/yzt-api/"

- 渠道ID：JYKJ
- 接入密钥：88dabcb59b8843bca413884f5920c202

### 签名加密方法
将参数通过 ASCII 码升序排序，通过 `key=value` 将参数拼接为字符串，前后加上私钥参数，对拼接的字符串进行 MD5 加密（小写）。  
例如：  
str = privateKey + key1=value1&key2=value2 + privateKey

统一 utf-8 编码，采用 **POST** 方式，提交内容放在 body 中间。  
将对象转 JSON 字符串后，调用 **BASE64** 进行加密，最后在 BODY 中传送的是 **BASE64 加密之后的字符串**。

设置请求头：  
Content-Type = text/plain

---

## 1. 短信验证码

- **URL 地址**：/biz/infoAd/api/sendSmsYzm

### 请求参数

| 参数        | 类型   | 可空 | 说明                                   |
|-------------|--------|------|----------------------------------------|
| userKey     | String | 否   | API 账户名，平台分配                  |
| phone       | String | 否   | 手机号                                 |
| sign        | String | 否   | MD5 签名，小写                        |
| adId        | String | 否   | 推广 ID，平台配置                     |
| timeStamp   | String | 否   | 时间戳 yyyymmddhh24miss                |
| userIp      | String | 否   | 用户 IP，不参与签名                   |
| sourceUrl   | String | 否   | 落地页，不参与签名                    |
| userAgent   | String | 否   | 用户浏览器信息，不参与签名             |
| appPackage  | String | 否   | APP 包名，如 com.ylb.ttas             |
| adPlatform  | String | 否   | 投放平台名，如 巨量、广点通            |
| appName     | String | 否   | APP 名称，如 豆包、百度                |
| cpParam     | String | 是   | 透传参数，不参与签名                   |

#### MD5 加密之前示例
7ab95ef9796b4a92a176d5629af32b0fadId=1Bs6pLVGG2k&phone=13981970000&timeStamp=20241025110501&userKey=TL_USER7ab95ef9796b4a92a176d5629af32b0f

#### JSON 示例
{
"adPlatform": "巨量",
"adid": "1Bs6pLVGG2k",
"appName": "抖音",
"appPackage": "com.xianyiqu.mnshy",
"phone": "13981970000",
"sign": "54b8f0ad86063f5c1a1f0e4f4be9caa8",
"sourceUrl": "https://******.cn/gluttony/page/?adsluid=wd9ntpnt23qlhw8",
"timeStamp": "20241025110501",
"userAgent": "Mozilla/5.0 (...)",
"userIp": "**************",
"userKey": "TL_USER"
}

#### 返回参数

| 参数    | 类型   | 可空 | 说明                              |
|---------|--------|------|-----------------------------------|
| state   | int    | 是   | 1=成功；7=号码省份达日限；5=下线；其它失败 |
| message | string | 是   | 提示信息                          |
| data    | string | 是   | 订单号                            |

#### 返回 JSON
{
"message": "发送成功",
"state": 1,
"data": "TEST_USER20240310143114100002"
}

---

## 2. 提交验证码

- **URL 地址**：/biz/infoAd/api/openBiz

### 请求参数

| 参数      | 类型   | 可空 | 说明                        |
|-----------|--------|------|-----------------------------|
| timeStamp | string | 否   | 订单时间: yyyyMMddhh24miss  |
| orderSn   | string | 否   | 短信接口返回的 data（订单号）|
| phone     | string | 否   | 电话号                      |
| smsCode   | string | 否   | 短信验证码                  |
| sign      | string | 否   | MD5 签名                    |
| userKey   | string | 否   | API 账户名                  |

#### 返回参数

| 参数    | 类型   | 可空 | 说明             |
|---------|--------|------|------------------|
| state   | int    | 否   | 1=成功，其它=失败 |
| message | string | 是   | 提示信息          |


## 3. 开通结果回调（渠道提供地址）

- **请求方式**：POST
- **请求头**：Content-Type = application/json

### 请求参数

| 参数         | 类型   | 可空 | 说明                |
|--------------|--------|------|---------------------|
| orderSn      | string | 否   | 订单号              |
| goodsSn      | string | 否   | 产品编码            |
| phoneNo      | string | 否   | 手机号              |
| behaviorType | string | 否   | 行为类型：1=订购成功，2=退订成功，3=订购失败 |
| orderTime    | string | 否   | 下单时间            |
| cpParam      | string | 是   | 透传参数            |
| handleMsg    | string | 否   | 处理描述            |

### 返回参数

| 参数    | 类型   | 可空 | 说明                |
|---------|--------|------|---------------------|
| state   | int    | 否   | 0=成功，其它=失败   |
| message | string | 是   | 提示信息            |
