package com.zbkj.front.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.constants.OrderConstants;
import com.zbkj.common.enums.ExteriorTypeEnum;
import com.zbkj.common.enums.OpenHandleTypeEnum;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.exception.OpenLog;
import com.zbkj.common.model.exception.OpenLogExt;
import com.zbkj.common.model.merchant.Merchant;
import com.zbkj.common.model.order.Order;
import com.zbkj.common.model.product.Product;
import com.zbkj.common.response.OrderNoResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.common.result.CommonResultCode;
import com.zbkj.common.result.OrderResultCode;
import com.zbkj.common.utils.CrmebUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.vo.ExteriorOrderCallbackResponse;
import com.zbkj.common.vo.ExteriorSendSmsRequest;
import com.zbkj.front.exterior.base.AbstractExteriorBase;
import com.zbkj.front.exterior.vo.*;
import com.zbkj.front.service.ExteriorFactory;
import com.zbkj.front.service.FrontOrderService;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 上游产品工厂类
 *
 * <AUTHOR>
 * @date 2025/4/5 8:51
 */

@Service
@Slf4j
public class ExteriorFactorySerice implements ExteriorFactory {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FrontOrderService frontOrderService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private ProductService productService;
    @Autowired
    private Map<String, AbstractExteriorBase> exteriorEngineMap = new ConcurrentHashMap<String, AbstractExteriorBase>(4);

    @Autowired
    private OpenLogService openLogService;

    @Autowired
    private OpenLogExtService openLogExtService;

    @Autowired
    private OrderService orderService;

    /**
     * 根据传入的参数返回对应的引擎
     *
     * @param exteriorTypeEnum
     * @return
     * @throws CrmebException
     */
    private AbstractExteriorBase getInstance(ExteriorTypeEnum exteriorTypeEnum) throws CrmebException {
        AbstractExteriorBase entryEngine = exteriorEngineMap.get(exteriorTypeEnum.getBeanName());
        if (entryEngine == null) {
            throw new CrmebException("not found engine,please check the configBean setting ");
        }
        return entryEngine;
    }

    @Override
    public CommonResult<ExteriorSendSmsResponse> sendSms(ExteriorSendSmsRequest request) {
        String resultMsg = null;
        try {
            if (request == null) {
                return CommonResult.failed("param error,request param not null");
            }
            if (request.getExteriorTypeEnum() == null) {
                return CommonResult.failed("must set exteriorTypeEnum param.");
            }
            if (request.getPhone() == null || request.getProductCode() == null) {
                return CommonResult.failed("必要參數缺失");
            }
            String myOrderNo = CrmebUtil.getOrderNo(OrderConstants.ORDER_PREFIX_PLATFORM);
            AbstractExteriorBase engine = getInstance(request.getExteriorTypeEnum());
            request.setPlatOrderNo(myOrderNo);
            CommonResult<ExteriorSendSmsResponse> result = engine.sendSms(request);
            resultMsg = JSON.toJSONString(result);
            if (result.getCode() == CommonResultCode.SUCCESS.getCode() && result.getData() != null) {
                result.getData().setPlatOrderNo(myOrderNo);
                result.getData().setOutOrderNo(request.getOutOrderNo());
                result.getData().setPhone(request.getPhone());
                result.getData().setProductCode(request.getProductCode());
                request.setChannelOrderNo(result.getData().getChannelOrderNo());
                redisUtil.set(request.getOutOrderNo(), request, Constants.SEND_MSG_TIME_OUT);
                result.getData().setChannelOrderNo(null);

            }
            return result;
        } catch (CrmebException e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("sendSms error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } catch (Exception e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("sendSms error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } finally {
            //写日志
            OpenLog log = openLogService.saveLog(request.getOutOrderNo(), OpenHandleTypeEnum.SEND_MSG,
                    request.getExteriorTypeEnum(), "", JSON.toJSONString(request), resultMsg);
            OpenLogExt openLogExt = new OpenLogExt();
            openLogExt.setLogId(log.getId());
            openLogExt.setExteriorTypeEnum(request.getExteriorTypeEnum().getDesc());
            openLogExt.setPlatOrderNo(request.getPlatOrderNo());
            openLogExt.setPhone(request.getPhone());
            openLogExt.setProductCode(request.getProductCode());
            openLogExt.setFirstContact(request.getFirstContact());
            openLogExt.setSecContact(request.getSecContact());
            openLogExt.setAppName(request.getAppName());
            openLogExt.setAppPackage(request.getAppPackage());
            openLogExt.setIp(request.getIp());
            openLogExt.setPageUrl(request.getPageUrl());
            openLogExt.setUa(request.getUa());
            openLogExt.setCreateTime(new Date());
            openLogExt.setUpdateTime(new Date());
            openLogExtService.save(openLogExt);
        }
    }

    @Override
    public CommonResult<ExteriorCreateOrderResponse> createOrder(ExteriorOrderRequest request, Merchant merchant) {
        String resultMsg = null;
        ExteriorSendSmsRequest sendSmsRequest = null;
        try {
            if (request == null) {
                return CommonResult.failed("param error,request param not null");
            }
            if (request.getExteriorTypeEnum() == null) {
                return CommonResult.failed("must set exteriorTypeEnum param.");
            }
            AbstractExteriorBase engine = getInstance(request.getExteriorTypeEnum());
            sendSmsRequest = (ExteriorSendSmsRequest) redisUtil.get(request.getOutOrderNo());
            if (sendSmsRequest == null) {
                return CommonResult.failed("渠道订单号不存在!");
            }
            if (!request.getPlatOrderNo().equals(sendSmsRequest.getPlatOrderNo())) {
                return CommonResult.failed("平台订单号不匹配!");
            }
            request.setSendSmsRequest(sendSmsRequest);
            CommonResult<ExteriorCreateOrderResponse> result = engine.createOrder(request);
            resultMsg = JSON.toJSONString(result);
            // 5. 如果上游接口调用成功，创建内部订单
            if (result.getCode() == CommonResultCode.SUCCESS.getCode() && result.getData() != null) {
                ExteriorCreateOrderResponse exteriorResponse = result.getData();
                String channelOrderNo = exteriorResponse.getChannelOrderNo();
                if(request.getExteriorTypeEnum().getCode().equals(ExteriorTypeEnum.YXWL.getCode())||request.getExteriorTypeEnum().getCode().equals(ExteriorTypeEnum.BJYX.getCode())){
                    //因银象在返回的channelOrderNo为空（在发送短信的时候已返回），所以需要获取短信发送请求中的channelOrderNo
                    channelOrderNo=sendSmsRequest.getChannelOrderNo();
                }
                if (StrUtil.isNotBlank(channelOrderNo)) {
                    try {
                        exteriorResponse.setPhone(request.getPhone());
                        exteriorResponse.setProductCode(request.getProductCode());
                        exteriorResponse.setPlatOrderNo(request.getPlatOrderNo());
                        exteriorResponse.setOutOrderNo(request.getOutOrderNo());
                        Product product = productService.getByProductCode(request.getProductCode(), merchant.getId());
                        // 目前调用外部接口用户信息暂时固定为0，后续如有需求可修改
                        Integer userId = 0;
                        // 创建内部订单
                        OrderNoResponse orderNoResponse = frontOrderService.createSimpleOrder(sendSmsRequest, product.getId(), 0, channelOrderNo, userId, request.getPhone(), request.getExteriorTypeEnum());
                        result.getData().setOutOrderNo(sendSmsRequest.getOutOrderNo());
                        result.getData().setChannelOrderNo(null);
                        result.getData().setPhone(request.getPhone());
                        result.getData().setProductCode(request.getProductCode());
                        result.getData().setPlatOrderNo(request.getPlatOrderNo());
                        return result;
                    } catch (Exception e) {
                        log.error("创建内部订单失败，商品编码：{}，渠道订单号：{}", request.getProductCode(), channelOrderNo, e);
                        // 不影响返回结果
                        resultMsg += ",创建内部订单失败:" + JSON.toJSONString(e);
                    }
                }
            } else {
                return CommonResult.failed(result != null ? result.getMessage() : "创建订单失败");
            }
        } catch (CrmebException e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("createOrder error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } catch (Exception e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("createOrder error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } finally {
            //写日志
            OpenLog log = openLogService.saveLog(request.getOutOrderNo(), OpenHandleTypeEnum.CREATE_ORDER,
                    request.getExteriorTypeEnum(), "", JSON.toJSONString(request), resultMsg);
            OpenLogExt openLogExt = new OpenLogExt();
            openLogExt.setLogId(log.getId());
            openLogExt.setExteriorTypeEnum(request.getExteriorTypeEnum().getDesc());
            openLogExt.setPlatOrderNo(request.getPlatOrderNo());
            openLogExt.setPhone(request.getPhone());
            openLogExt.setProductCode(request.getProductCode());
            openLogExt.setFirstContact(sendSmsRequest.getFirstContact());
            openLogExt.setSecContact(sendSmsRequest.getSecContact());
            openLogExt.setAppName(sendSmsRequest.getAppName());
            openLogExt.setAppPackage(sendSmsRequest.getAppPackage());
            openLogExt.setIp(sendSmsRequest.getIp());
            openLogExt.setPageUrl(sendSmsRequest.getPageUrl());
            openLogExt.setUa(sendSmsRequest.getUa());
            openLogExt.setCreateTime(new Date());
            openLogExt.setUpdateTime(new Date());
            openLogExtService.save(openLogExt);
        }
        return CommonResult.failed("创建订单失败");
    }

    @Override
    public CommonResult<ExteriorQueryOrderResponse> queryOrder(ExteriorQueryOrderRequest request) {
        String resultMsg = null;
        try {
            if (request == null) {
                return CommonResult.failed("param error,request param not null");
            }
            if (request.getExteriorTypeEnum() == null) {
                return CommonResult.failed("must set exteriorTypeEnum param.");
            }
            Order order = orderService.getByOrderNo(request.getOutOrderNo());
            if (order == null || StrUtil.isBlank(order.getPlatOrderNo())) {
                throw new CrmebException(OrderResultCode.ORDER_NOT_EXIST);
            }
            AbstractExteriorBase engine = getInstance(request.getExteriorTypeEnum());
            request.setPlatOrderNo(order.getPlatOrderNo());
            CommonResult<ExteriorQueryOrderResponse> result = engine.queryOrder(request);
            resultMsg = JSON.toJSONString(result);
            if (result.getCode() == CommonResultCode.SUCCESS.getCode() && result.getData() != null) {
                result.getData().setOutOrderNo(request.getOutOrderNo());
            }
            return result;
        } catch (CrmebException e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("queryOrder error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } catch (Exception e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("queryOrder error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } finally {
            //写日志
            openLogService.saveLog(request.getOutOrderNo(), OpenHandleTypeEnum.QUERY_ORDER,
                    request.getExteriorTypeEnum(), "", JSON.toJSONString(request), resultMsg);
        }
    }

    @Override
    public CommonResult handleCallback(ExteriorOrderCallbackResponse request) {
        String resultMsg = null;
        try {
            if (request == null) {
                return CommonResult.failed("param error,request param not null");
            }
            if (request.getExteriorTypeEnum() == null) {
                return CommonResult.failed("must set exteriorTypeEnum param.");
            }
            AbstractExteriorBase engine = getInstance(request.getExteriorTypeEnum());
            return engine.handleCallback(request);
        } catch (CrmebException e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("queryOrder error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } catch (Exception e) {
            resultMsg = JSON.toJSONString(e);
            //记录日志
            log.error("queryOrder error,request:{},error:{}", request, e.getMessage());
            return CommonResult.failed(e.getMessage());
        } finally {
            //写日志
            openLogService.saveLog(request.getOutOrderNo(), OpenHandleTypeEnum.CALLBACK,
                    request.getExteriorTypeEnum(), "", JSON.toJSONString(request), resultMsg);
        }
    }
}
